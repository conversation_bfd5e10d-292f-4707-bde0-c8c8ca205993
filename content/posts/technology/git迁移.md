---
title: "Flutter开发者必备：完整命令行工具指南"
slug: "flutter-cli-commands-guide"
description: "全面的Flutter命令行工具使用指南，包含开发、构建、调试、依赖管理等各个阶段的常用命令及实践技巧"
tags: ["Flutter", "移动开发", "命令行", "工具"]
keywords: ["Flutter命令", "移动开发", "CLI工具", "开发指南"]
categories: ["技术"]
authors: "wenhao"
date: "2025-01-23"
status: "Published"
image: "https://image.wenhaofree.com/2025/05/9240d7d9a83d2cdc0381f3160780d21b.png"
---


# Git 仓库迁移到新远程仓库地址操作指南

将一个仓库“搬家”到新的远程地址，只需添加新远程并推送即可。下面是详细操作步骤。

## 步骤说明

就像把家里的东西搬到新房子，先告诉 Git 新家地址，再搬运所有内容。

### 1. 添加新远程仓库

以 `new-origin` 为例，执行：

```bash
git remote add new-origin 新仓库地址
```

### 2. 推送所有分支

**注意：参数前是两个短横线 `--`，不要用中文长横线。**

```bash
git push new-origin --all
```

### 3. 推送所有标签

```bash
git push new-origin --tags
```

### 4. （可选）将新远程改为默认

如果以后只用新仓库，可以将 `origin` 改为新地址：

```bash
git remote remove origin
git remote rename new-origin origin
```

## 总结

通过上述步骤，原仓库的所有内容（分支、标签）就会同步到新仓库。适用于项目迁移、备份或更换远程平台等场景。