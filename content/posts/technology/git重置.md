## Git 常用回退与分支合并操作指南

本指南梳理了在项目开发过程中，如何将分支回退到指定 commit、强制推送、以及将回退分支合并到主分支的标准操作流程。

---

### 1. 回退分支到指定 Commit

#### 1.1 查看提交历史

```bash
git log --oneline
```
找到目标 commit 的 hash 值（如 `0d177db`）。

#### 1.2 回退分支到指定 commit

**仅本地回退（不影响远程）：**
```bash
git checkout 0d177db
```
此时处于“游离 HEAD”状态，建议新建分支继续开发：
```bash
git checkout -b restore-0d177db
```

**强制回退当前分支（覆盖历史）：**
```bash
git reset --hard 0d177db
```
推送到远程（慎用，会覆盖远程历史）：
```bash
git push -f origin 分支名
```

---

### 2. 本地与远程分支同步说明

本地操作不会自动同步到 GitHub，需执行 push：
- 普通推送（不覆盖历史）：`git push origin 分支名`
- 强制推送（覆盖远程历史）：`git push -f origin 分支名`

---

### 3. 合并回退分支到主分支

假设已创建了 `restore-0d177db` 分支。

#### 3.1 切换到主分支

```bash
git checkout main
```

#### 3.2 合并回退分支

```bash
git merge restore-0d177db
```
如有冲突，解决后：
```bash
git add .
git commit
```

#### 3.3 推送主分支到远程

```bash
git push origin main
```

---

### 4. 用回退分支完全替换主分支（可选）

如果要让 main 分支内容完全等于 `restore-0d177db`（丢弃 main 现有历史），可使用：

```bash
git checkout restore-0d177db
git branch -f main
git checkout main
git push -f origin main
```

---

### 5. 注意事项

- 强制推送 (`-f`) 会覆盖远程历史，团队协作请提前沟通。
- 合并分支时如遇冲突，需手动解决。
- 建议操作前做好备份。